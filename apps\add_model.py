import os
import sys
import json
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk

# Add parent directory to path to allow importing from lib and utils
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import utility functions
try:
    from utils import get_app_dir, get_user_data_dir, save_json_config, get_config_path
    from lib.theme_utils import apply_theme
except ImportError:
    try:
        from lib.utils import get_app_dir, get_user_data_dir, save_json_config, get_config_path
        from lib.theme_utils import apply_theme
    except ImportError:
        from lib.manual_creator import get_app_dir, get_user_data_dir, get_config_path

        # Define save_json_config if not available
        def save_json_config(filename, data):
            """Save a JSON configuration file to the user data directory"""
            user_dir = get_user_data_dir()
            if not os.path.exists(user_dir):
                os.makedirs(user_dir, exist_ok=True)
            config_path = os.path.join(user_dir, filename)

            try:
                with open(config_path, 'w') as f:
                    json.dump(data, f, indent=4)
                return True
            except Exception as e:
                print(f"Failed to save {filename}: {str(e)}")
                return False

        # Define a basic theme utility if import fails
        def apply_theme(theme_name="red", appearance_mode="dark"):
            ctk.set_appearance_mode(appearance_mode)
            ctk.set_default_color_theme("blue")

class AddModelApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Add Model to Engineering Tools")
        self.root.geometry("700x900")

        # Apply the red theme
        apply_theme("red", "dark")

        # Create main frame
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="Add New Model",
            font=("Arial", 20, "bold")
        )
        title_label.pack(pady=10)

        # Description
        desc_label = ctk.CTkLabel(
            self.main_frame,
            text="Add a new model to the Engineering Tools configuration.",
            font=("Arial", 12)
        )
        desc_label.pack(pady=10)

        # Category selection
        self.category_frame = ctk.CTkFrame(self.main_frame)
        self.category_frame.pack(fill="x", pady=10)

        category_label = ctk.CTkLabel(
            self.category_frame,
            text="Category:",
            font=("Arial", 12, "bold")
        )
        category_label.pack(anchor="w", padx=10, pady=5)

        # Load existing categories
        self.categories = self.get_existing_categories()

        self.category_var = tk.StringVar()
        self.category_combobox = ctk.CTkComboBox(
            self.category_frame,
            values=self.categories,
            variable=self.category_var,
            width=500,
            command=self.on_category_select
        )
        # Set default value to first category
        if self.categories:
            self.category_combobox.set(self.categories[0])
        self.category_combobox.pack(padx=10, pady=5)

        # New category option
        self.new_category_frame = ctk.CTkFrame(self.main_frame)
        self.new_category_frame.pack(fill="x", pady=10)

        new_category_label = ctk.CTkLabel(
            self.new_category_frame,
            text="Create New Category:",
            font=("Arial", 12, "bold"),
            text_color="#2AA876"  # Use the same green color as the button
        )
        new_category_label.pack(anchor="w", padx=10, pady=5)

        # Add a hint label
        new_category_hint = ctk.CTkLabel(
            self.new_category_frame,
            text="Select '-- Create New Category --' from the dropdown or enter a name directly here",
            font=("Arial", 10),
            text_color="#888888"  # Light gray for hint text
        )
        new_category_hint.pack(anchor="w", padx=10)

        self.new_category_var = tk.StringVar()
        self.new_category_entry = ctk.CTkEntry(
            self.new_category_frame,
            textvariable=self.new_category_var,
            width=500,
            placeholder_text="Enter new category name here"
        )
        self.new_category_entry.pack(padx=10, pady=5)

        # Model name
        self.model_frame = ctk.CTkFrame(self.main_frame)
        self.model_frame.pack(fill="x", pady=10)

        model_label = ctk.CTkLabel(
            self.model_frame,
            text="Model Name:",
            font=("Arial", 12, "bold")
        )
        model_label.pack(anchor="w", padx=10, pady=5)

        self.model_var = tk.StringVar()
        self.model_entry = ctk.CTkEntry(
            self.model_frame,
            textvariable=self.model_var,
            width=500
        )
        self.model_entry.pack(padx=10, pady=5)

        # Template path
        self.template_frame = ctk.CTkFrame(self.main_frame)
        self.template_frame.pack(fill="x", pady=10)

        template_label = ctk.CTkLabel(
            self.template_frame,
            text="Template File:",
            font=("Arial", 12, "bold")
        )
        template_label.pack(anchor="w", padx=10, pady=5)

        self.template_path_var = tk.StringVar()
        self.template_path_entry = ctk.CTkEntry(
            self.template_frame,
            textvariable=self.template_path_var,
            width=400
        )
        self.template_path_entry.pack(side="left", padx=10, pady=5)

        template_browse_btn = ctk.CTkButton(
            self.template_frame,
            text="Browse",
            command=self.browse_template_file
        )
        template_browse_btn.pack(side="right", padx=10, pady=5)

        # Drawings path
        self.drawings_frame = ctk.CTkFrame(self.main_frame)
        self.drawings_frame.pack(fill="x", pady=10)

        drawings_label = ctk.CTkLabel(
            self.drawings_frame,
            text="Drawings Directory:",
            font=("Arial", 12, "bold")
        )
        drawings_label.pack(anchor="w", padx=10, pady=5)

        self.drawings_path_var = tk.StringVar()
        self.drawings_path_entry = ctk.CTkEntry(
            self.drawings_frame,
            textvariable=self.drawings_path_var,
            width=400
        )
        self.drawings_path_entry.pack(side="left", padx=10, pady=5)

        drawings_browse_btn = ctk.CTkButton(
            self.drawings_frame,
            text="Browse",
            command=self.browse_drawings_dir
        )
        drawings_browse_btn.pack(side="right", padx=10, pady=5)

        # ASME Flag
        self.asme_frame = ctk.CTkFrame(self.main_frame)
        self.asme_frame.pack(fill="x", pady=10)

        asme_label = ctk.CTkLabel(
            self.asme_frame,
            text="ASME Flag:",
            font=("Arial", 12, "bold")
        )
        asme_label.pack(anchor="w", padx=10, pady=5)

        self.asme_var = tk.BooleanVar(value=False)
        self.asme_checkbox = ctk.CTkCheckBox(
            self.asme_frame,
            text="ASME Certified",
            variable=self.asme_var
        )
        self.asme_checkbox.pack(padx=10, pady=5)

        # Controls Parent
        self.controls_frame = ctk.CTkFrame(self.main_frame)
        self.controls_frame.pack(fill="x", pady=10)

        controls_label = ctk.CTkLabel(
            self.controls_frame,
            text="Controls Parent ID:",
            font=("Arial", 12, "bold")
        )
        controls_label.pack(anchor="w", padx=10, pady=5)

        self.controls_var = tk.StringVar()
        self.controls_entry = ctk.CTkEntry(
            self.controls_frame,
            textvariable=self.controls_var,
            width=500
        )
        self.controls_entry.pack(padx=10, pady=5)

        # Add button
        self.add_btn = ctk.CTkButton(
            self.main_frame,
            text="Add Model",
            command=self.add_model,
            height=40,
            font=("Arial", 14, "bold"),
            fg_color="#2AA876",
            hover_color="#22815D"
        )
        self.add_btn.pack(pady=20)

        # Status label
        self.status_var = tk.StringVar()
        self.status_label = ctk.CTkLabel(
            self.main_frame,
            textvariable=self.status_var,
            font=("Arial", 12),
            text_color="#2AA876"
        )
        self.status_label.pack(pady=10)

    def get_existing_categories(self):
        """Get existing categories from models.json"""
        try:
            models_data = self.load_models_json()
            categories = list(models_data.keys())
            # Always add a 'Create New Category' option at the beginning
            categories = ["-- Create New Category --"] + categories
            return categories
        except Exception as e:
            print(f"Error getting categories: {e}")
            return ["-- Create New Category --"]

    def load_models_json(self):
        """Load models.json from user directory or app directory"""
        try:
            # First try to get the config path using the utility function
            config_path = get_config_path("models.json")
            print(f"Looking for models.json using get_config_path: {config_path}")

            if os.path.exists(config_path):
                print(f"Found models.json at {config_path}")
                try:
                    with open(config_path, 'r') as f:
                        data = json.load(f)
                        print(f"Loaded categories: {list(data.keys())}")
                        # Convert old array format to new object format if needed
                        data = self.convert_models_format(data)
                        return data
                except Exception as e:
                    print(f"Error loading from {config_path}: {e}")

            # Fallback to checking multiple locations if get_config_path didn't work
            # First try current working directory (most likely during development)
            current_dir_path = os.path.join(os.getcwd(), 'models.json')
            print(f"Looking for models.json in current working directory: {current_dir_path}")

            if os.path.exists(current_dir_path):
                print(f"Found models.json in current working directory")
                try:
                    with open(current_dir_path, 'r') as f:
                        data = json.load(f)
                        print(f"Loaded categories: {list(data.keys())}")
                        # Convert old array format to new object format if needed
                        data = self.convert_models_format(data)
                        return data
                except Exception as e:
                    print(f"Error loading from current working directory: {e}")

            # Then try user directory
            user_dir = get_user_data_dir()
            user_models_path = os.path.join(user_dir, 'models.json')
            print(f"Looking for models.json in user directory: {user_models_path}")

            if os.path.exists(user_models_path):
                print(f"Found models.json in user directory")
                try:
                    with open(user_models_path, 'r') as f:
                        data = json.load(f)
                        print(f"Loaded categories: {list(data.keys())}")
                        # Convert old array format to new object format if needed
                        data = self.convert_models_format(data)
                        return data
                except Exception as e:
                    print(f"Error loading from user directory: {e}")

            # Then try app directory resources/config folder
            app_dir = get_app_dir()
            app_models_path = os.path.join(app_dir, 'resources', 'config', 'models.json')
            print(f"Looking for models.json in app resources: {app_models_path}")

            if os.path.exists(app_models_path):
                print(f"Found models.json in app resources")
                try:
                    with open(app_models_path, 'r') as f:
                        data = json.load(f)
                        print(f"Loaded categories: {list(data.keys())}")
                        # Convert old array format to new object format if needed
                        data = self.convert_models_format(data)
                        return data
                except Exception as e:
                    print(f"Error loading from app resources: {e}")

            # Finally try app directory root
            current_models_path = os.path.join(app_dir, 'models.json')
            print(f"Looking for models.json in app directory: {current_models_path}")

            if os.path.exists(current_models_path):
                print(f"Found models.json in app directory")
                try:
                    with open(current_models_path, 'r') as f:
                        data = json.load(f)
                        print(f"Loaded categories: {list(data.keys())}")
                        # Convert old array format to new object format if needed
                        data = self.convert_models_format(data)
                        return data
                except Exception as e:
                    print(f"Error loading from app directory: {e}")

            # If no models.json found, return empty dict
            print("No models.json found in any location")
            return {}
        except Exception as e:
            print(f"Error in load_models_json: {e}")
            return {}

    def convert_models_format(self, data):
        """Convert old array format to new object format if needed"""
        try:
            converted_data = {}
            for category, models in data.items():
                converted_data[category] = {}
                for model_name, model_data in models.items():
                    # Check if it's old array format [template_path, drawings_path, asme_flag, controls_parent]
                    if isinstance(model_data, list) and len(model_data) >= 4:
                        print(f"Converting {category}/{model_name} from array to object format")
                        converted_data[category][model_name] = {
                            'template_path': model_data[0],
                            'drawings_path': model_data[1],
                            'asme_flag': model_data[2],
                            'controls_parent': model_data[3]
                        }
                    # Check if it's already new object format
                    elif isinstance(model_data, dict):
                        converted_data[category][model_name] = model_data
                    else:
                        print(f"Warning: Unknown format for {category}/{model_name}: {model_data}")
                        # Try to handle as best we can
                        converted_data[category][model_name] = model_data

            return converted_data
        except Exception as e:
            print(f"Error converting models format: {e}")
            return data

    def update_original_models_file(self, models_data):
        """Update the original resources/config/models.json file if it exists"""
        try:
            app_dir = get_app_dir()
            original_models_path = os.path.join(app_dir, 'resources', 'config', 'models.json')

            if os.path.exists(original_models_path):
                print(f"Updating original models.json at: {original_models_path}")
                try:
                    with open(original_models_path, 'w') as f:
                        json.dump(models_data, f, indent=4)
                    print("Successfully updated original models.json")
                except Exception as e:
                    print(f"Failed to update original models.json: {e}")
                    # Don't raise the error, just log it since the main save already succeeded
            else:
                print(f"Original models.json not found at: {original_models_path}")
        except Exception as e:
            print(f"Error in update_original_models_file: {e}")
            # Don't raise the error, just log it since the main save already succeeded

    def browse_template_file(self):
        """Browse for template file"""
        file_path = filedialog.askopenfilename(
            title="Select Template File",
            filetypes=[("Word Templates", "*.dotx"), ("All Files", "*.*")]
        )
        if file_path:
            self.template_path_var.set(file_path)

    def browse_drawings_dir(self):
        """Browse for drawings directory"""
        directory = filedialog.askdirectory(title="Select Drawings Directory")
        if directory:
            self.drawings_path_var.set(directory)

    def on_category_select(self, selection):
        """Handle category dropdown selection"""
        if selection == "-- Create New Category --":
            # Clear any existing value
            self.new_category_var.set("")
            # Focus on the new category entry field
            self.new_category_entry.focus_set()
            # Highlight the field to make it obvious to the user
            self.new_category_frame.configure(border_width=2, border_color="#2AA876")
        else:
            # Reset the highlight if a regular category is selected
            self.new_category_frame.configure(border_width=0)

    def add_model(self):
        """Add a new model to models.json"""
        try:
            # Get values from form
            # If new category is provided, use it; otherwise use the selected category
            # But don't use the selected category if it's the "Create New Category" option
            selected_category = self.category_var.get()
            if selected_category == "-- Create New Category --":
                selected_category = ""

            category = self.new_category_var.get() if self.new_category_var.get() else selected_category
            model_name = self.model_var.get()
            template_path = self.template_path_var.get()
            drawings_path = self.drawings_path_var.get()
            asme_flag = self.asme_var.get()
            controls_parent = self.controls_var.get()

            # Validate inputs
            if not category:
                messagebox.showerror("Error", "Please select or enter a category")
                return

            if not model_name:
                messagebox.showerror("Error", "Please enter a model name")
                return

            if not template_path:
                messagebox.showerror("Error", "Please select a template file")
                return

            if not drawings_path:
                messagebox.showerror("Error", "Please select a drawings directory")
                return

            # Load existing models.json
            try:
                models_data = self.load_models_json()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load models.json: {str(e)}")
                return

            # Create category if it doesn't exist
            if category not in models_data:
                models_data[category] = {}

            # Check if model already exists
            if model_name in models_data[category]:
                overwrite = messagebox.askyesno(
                    "Model Exists",
                    f"Model '{model_name}' already exists in category '{category}'. Overwrite?"
                )
                if not overwrite:
                    return

            # Add new model using the new format with keys
            models_data[category][model_name] = {
                'template_path': template_path,
                'drawings_path': drawings_path,
                'asme_flag': asme_flag,
                'controls_parent': controls_parent
            }

            # Save updated models.json
            try:
                # Save to user data directory (primary location)
                save_json_config('models.json', models_data)

                # Also update the original resources/config/models.json if it exists
                self.update_original_models_file(models_data)

                self.status_var.set(f"Added model '{model_name}' to category '{category}'")
                messagebox.showinfo(
                    "Success",
                    f"Added model '{model_name}' to category '{category}'"
                )

                # Clear form for next entry
                self.model_var.set("")
                self.template_path_var.set("")
                self.drawings_path_var.set("")
                self.asme_var.set(False)
                self.controls_var.set("")

                # Update categories list
                self.categories = self.get_existing_categories()
                self.category_combobox.configure(values=self.categories)
                if self.categories:
                    self.category_combobox.set(self.categories[0])

            except Exception as e:
                messagebox.showerror("Error", f"Failed to save models.json: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"An unexpected error occurred: {str(e)}")
            print(f"Unexpected error in add_model: {e}")

def check_models_json():
    """Debug function to check if models.json exists and display its contents"""
    # Check current working directory
    current_dir_path = os.path.join(os.getcwd(), 'models.json')
    print(f"\nChecking for models.json in: {current_dir_path}")
    if os.path.exists(current_dir_path):
        print(f"File exists! Size: {os.path.getsize(current_dir_path)} bytes")
        try:
            with open(current_dir_path, 'r') as f:
                data = json.load(f)
                print(f"File contains {len(data)} categories: {list(data.keys())}")
                for category, models in data.items():
                    print(f"  {category}: {len(models)} models")
        except Exception as e:
            print(f"Error reading file: {e}")
    else:
        print("File does not exist in current directory")

    # Check user data directory
    user_dir = get_user_data_dir()
    user_path = os.path.join(user_dir, 'models.json')
    print(f"\nChecking for models.json in: {user_path}")
    if os.path.exists(user_path):
        print(f"File exists! Size: {os.path.getsize(user_path)} bytes")
    else:
        print("File does not exist in user directory")

def create_sample_models_json():
    """Create a sample models.json file in the current directory for testing"""
    sample_data = {
        "Chiller": {
            "P408": {
                "template_path": "C:\\Templates\\Chiller\\Small Chiller Manual Template.dotx",
                "drawings_path": "C:\\Drawings\\Chiller\\P408",
                "asme_flag": False,
                "controls_parent": "503390"
            },
            "P410": {
                "template_path": "C:\\Templates\\Chiller\\Small Chiller Manual Template.dotx",
                "drawings_path": "C:\\Drawings\\Chiller\\P410",
                "asme_flag": False,
                "controls_parent": "503390"
            }
        },
        "Heat Exchanger": {
            "PHI-110P": {
                "template_path": "C:\\Templates\\Heat Exchanger\\Heat Exchanger Manual Template.dotx",
                "drawings_path": "C:\\Drawings\\Heat Exchanger\\PHI-110P",
                "asme_flag": True,
                "controls_parent": "501281"
            }
        },
        "Pump": {
            "PSP-4": {
                "template_path": "C:\\Templates\\Pump\\Pump Manual Template.dotx",
                "drawings_path": "C:\\Drawings\\Pump",
                "asme_flag": False,
                "controls_parent": "500432"
            }
        }
    }

    current_dir_path = os.path.join(os.getcwd(), 'models.json')
    if not os.path.exists(current_dir_path):
        try:
            with open(current_dir_path, 'w') as f:
                json.dump(sample_data, f, indent=4)
            print(f"Created sample models.json file at {current_dir_path}")
            return True
        except Exception as e:
            print(f"Error creating sample file: {e}")
            return False
    else:
        print("models.json already exists, not overwriting")
        return False

if __name__ == "__main__":
    # Run the debug function first
    check_models_json()

    # Create a sample models.json file if it doesn't exist
    models_exists = os.path.exists(os.path.join(os.getcwd(), 'models.json'))
    if not models_exists:
        print("\nNo models.json found. Creating a sample file for testing...")
        create_sample_models_json()
        check_models_json()  # Check again after creating

    # Then start the application
    root = ctk.CTk()
    app = AddModelApp(root)
    root.mainloop()
